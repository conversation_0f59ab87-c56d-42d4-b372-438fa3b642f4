# 无人车编队围捕实验

## 实验概述

本项目实现了一个无人车编队围捕实验的仿真程序，模拟四辆无人车对一辆故障车进行协同围捕的场景。

## 实验要求

- **地图环境**: 方形地图，四辆无人车位于地图角落，一辆故障无人车位于地图中央
- **控制目标**: 四辆无人车采用一致性控制协议完成编队控制及对故障无人车的围捕
- **编队形状**: 四辆无人车呈正方形编队，且故障无人车无逃跑路径时认定任务完成
- **物理约束**: 考虑无人车避碰等实际物理约束

## 技术特点

### 1. 车辆模型
- 每辆车具有位置、速度、加速度等物理属性
- 考虑最大速度和最大加速度限制
- 具备碰撞检测和安全距离控制

### 2. 控制算法

#### 编队控制
- 基于期望位置的比例控制
- 四辆车围绕目标车形成正方形编队
- 动态调整编队半径

#### 一致性控制协议
- 邻居车辆间的速度一致性
- 基于通信范围的分布式控制
- 确保编队的协调性

#### 碰撞避免
- 基于人工势场的排斥力
- 安全距离内产生排斥力
- 防止车辆间碰撞

#### 边界控制
- 防止车辆超出地图边界
- 边界附近产生约束力

### 3. 物理约束
- **速度限制**: 每辆车都有最大速度限制
- **加速度限制**: 限制车辆的最大加速度，模拟真实物理特性
- **碰撞检测**: 实时检测车辆间的碰撞风险
- **安全距离**: 维持车辆间的最小安全距离
- **边界约束**: 确保所有车辆在地图范围内运行

## 安装和运行

### 环境要求
- Python 3.7+
- NumPy
- Matplotlib

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行程序
```bash
python drone_formation_capture.py
```

程序提供两种运行模式：
1. **可视化仿真**: 实时显示车辆运动动画
2. **分析仿真**: 运行仿真并生成性能分析图表

## 程序结构

### 主要类

#### Vehicle类
- 表示单个无人车
- 包含位置、速度、物理属性
- 提供碰撞检测和位置更新功能

#### FormationController类
- 编队控制器
- 实现各种控制算法
- 协调多车辆的运动

#### Simulation类
- 仿真主控制器
- 管理仿真时间和状态
- 协调各组件的交互

### 控制参数

可以通过修改以下参数来调整控制性能：

```python
# 编队控制参数
self.formation_radius = 2.0      # 编队半径
self.k_formation = 1.0           # 编队控制增益
self.k_consensus = 0.8           # 一致性控制增益
self.k_avoidance = 2.0           # 避障控制增益
self.k_boundary = 1.5            # 边界控制增益

# 车辆物理参数
self.max_speed = 1.0             # 最大速度
self.max_acceleration = 0.5      # 最大加速度
self.safety_distance = 0.8       # 安全距离
self.communication_range = 5.0   # 通信范围
```

## 实验结果

### 性能指标
1. **编队误差**: 车辆实际位置与期望位置的偏差
2. **平均距离**: 围捕车辆到目标车的平均距离
3. **收敛时间**: 达到稳定编队所需的时间
4. **碰撞次数**: 仿真过程中的碰撞事件统计

### 预期效果
- 四辆车从角落出发，逐渐向目标车靠近
- 形成稳定的正方形编队围绕目标车
- 即使目标车随机移动，编队也能保持稳定
- 无碰撞发生，满足安全约束

## 扩展功能

### 可能的改进方向
1. **动态编队**: 根据环境变化调整编队形状
2. **路径规划**: 加入全局路径规划算法
3. **通信延迟**: 模拟真实的通信延迟和丢包
4. **传感器噪声**: 加入位置和速度测量噪声
5. **多目标**: 扩展到多个目标车的围捕

## 注意事项

1. 程序运行时会打开matplotlib窗口显示动画
2. 可以通过修改参数来观察不同的控制效果
3. 如果出现收敛困难，可以调整控制增益参数
4. 建议在性能较好的计算机上运行以获得流畅的动画效果

## 技术支持

如有问题，请检查：
1. Python版本是否符合要求
2. 依赖包是否正确安装
3. 参数设置是否合理

---

**实验目标**: 通过本实验，学习和掌握多智能体系统的协同控制、编队控制和一致性算法的实际应用。