import pygame
import random
import math

# --- Constants ---
SCREEN_WIDTH = 1100
SCREEN_HEIGHT = 850
MAP_WIDTH = 1000
MAP_HEIGHT = 750
MAP_MARGIN = (SCREEN_WIDTH - MAP_WIDTH) // 2

FPS = 60

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
UNIFIED_PURSUER_COLOR = (0, 120, 255)
EVADER_COLOR = RED
GRID_COLOR = (220, 220, 220)

# Vehicle Properties
VEHICLE_RADIUS = 15
PURSUER_MAX_SPEED = 140.0  # pixels/second
PURSUER_MAX_FORCE = 550.0  # Significantly increased pursuer force
EVADER_MAX_SPEED = 105.0   # pixels/second (Slightly reduced)
EVADER_MAX_FORCE = 290.0   # pixels/second^2

# Formation Properties
FORMATION_SIDE_LENGTH = 65  # Target a tighter formation
CAPTURE_TOLERANCE = VEHICLE_RADIUS * 1.2 
EFFECTIVE_CAPTURE_RADIUS_THRESHOLD = FORMATION_SIDE_LENGTH * 0.75 # (65 * 0.75 = 48.75)
MAX_ALLOWED_ANGULAR_GAP = math.radians(115) # Stricter: 115 degrees for more square-like
MIN_ESCAPE_GAP_FACTOR = 2.3 # Stricter: Evader needs less space to be considered "escapable"

# Collision Avoidance Properties
COLLISION_BUFFER = VEHICLE_RADIUS * 1.1 
AVOIDANCE_WEIGHT = 4.0 
BOUNDARY_REPEL_STRENGTH = 1.8 

# Evader Wander Properties
WANDER_CIRCLE_DIST = 60
WANDER_CIRCLE_RADIUS = 25
WANDER_ANGLE_CHANGE = 0.3
EVADER_BOUNDARY_AVOID_MARGIN = 80 
EVADER_BOUNDARY_AVOID_FORCE_SCALE = 1.2 

class Vehicle:
    def __init__(self, x, y, role, id_num, color, radius, max_speed, max_force):
        self.pos = pygame.Vector2(x + MAP_MARGIN, y + MAP_MARGIN)
        self.vel = pygame.Vector2(random.uniform(-1, 1), random.uniform(-1, 1))
        if self.vel.length_squared() > 0:
            self.vel = self.vel.normalize() * random.uniform(0, max_speed * 0.3) 
        else:
            self.vel = pygame.Vector2(0,0)
        self.acc = pygame.Vector2(0, 0)
        self.role = role
        self.id = id_num
        self.color = color
        self.radius = radius
        self.max_speed = max_speed
        self.max_force = max_force

        if self.role == 'evader':
            self.wander_angle = random.uniform(0, 2 * math.pi)

    def apply_force(self, force_vector):
        self.acc += force_vector

    def _seek(self, target_pos):
        desired_vel_vec = target_pos - self.pos
        dist_to_target = desired_vel_vec.length()

        if dist_to_target == 0:
            return pygame.Vector2(0,0)
        
        desired_vel = desired_vel_vec.normalize() * self.max_speed
        
        steer_force = desired_vel - self.vel
        if steer_force.length() > self.max_force:
            steer_force.scale_to_length(self.max_force)
        return steer_force

    def _wander(self, dt):
        circle_center_rel = self.vel.copy()
        if circle_center_rel.length_squared() == 0: 
            angle = random.uniform(0, 2 * math.pi)
            circle_center_rel = pygame.Vector2(math.cos(angle), math.sin(angle))
        
        circle_center_rel.scale_to_length(WANDER_CIRCLE_DIST)
        self.wander_angle += random.uniform(-WANDER_ANGLE_CHANGE, WANDER_ANGLE_CHANGE) 
        h = pygame.Vector2(math.cos(self.wander_angle), math.sin(self.wander_angle)) * WANDER_CIRCLE_RADIUS
        wander_target_rel = circle_center_rel + h
        steer_force = wander_target_rel.normalize() * self.max_force 
        return steer_force

    def _avoid_map_boundaries_evader(self):
        steer_accumulator = pygame.Vector2(0,0)
        map_pos_x = self.pos.x - MAP_MARGIN
        map_pos_y = self.pos.y - MAP_MARGIN
        # panic_margin = EVADER_BOUNDARY_AVOID_MARGIN * 0.5 # Not currently used, but idea for future

        if map_pos_x < EVADER_BOUNDARY_AVOID_MARGIN:
            penetration = EVADER_BOUNDARY_AVOID_MARGIN - map_pos_x
            scale = EVADER_BOUNDARY_AVOID_FORCE_SCALE + (penetration / EVADER_BOUNDARY_AVOID_MARGIN) * 1.5
            desired_vel = pygame.Vector2(self.max_speed, self.vel.y) 
            steer_force_component = (desired_vel - self.vel) * scale
            if steer_force_component.length() > self.max_force * scale: 
                 steer_force_component.scale_to_length(self.max_force * scale)
            steer_accumulator += steer_force_component
        elif map_pos_x > MAP_WIDTH - EVADER_BOUNDARY_AVOID_MARGIN:
            penetration = map_pos_x - (MAP_WIDTH - EVADER_BOUNDARY_AVOID_MARGIN)
            scale = EVADER_BOUNDARY_AVOID_FORCE_SCALE + (penetration / EVADER_BOUNDARY_AVOID_MARGIN) * 1.5
            desired_vel = pygame.Vector2(-self.max_speed, self.vel.y)
            steer_force_component = (desired_vel - self.vel) * scale
            if steer_force_component.length() > self.max_force * scale:
                 steer_force_component.scale_to_length(self.max_force * scale)
            steer_accumulator += steer_force_component
        
        if map_pos_y < EVADER_BOUNDARY_AVOID_MARGIN:
            penetration = EVADER_BOUNDARY_AVOID_MARGIN - map_pos_y
            scale = EVADER_BOUNDARY_AVOID_FORCE_SCALE + (penetration / EVADER_BOUNDARY_AVOID_MARGIN) * 1.5
            desired_vel = pygame.Vector2(self.vel.x, self.max_speed)
            steer_force_component = (desired_vel - self.vel) * scale
            if steer_force_component.length() > self.max_force * scale:
                 steer_force_component.scale_to_length(self.max_force * scale)
            steer_accumulator += steer_force_component
        elif map_pos_y > MAP_HEIGHT - EVADER_BOUNDARY_AVOID_MARGIN:
            penetration = map_pos_y - (MAP_HEIGHT - EVADER_BOUNDARY_AVOID_MARGIN)
            scale = EVADER_BOUNDARY_AVOID_FORCE_SCALE + (penetration / EVADER_BOUNDARY_AVOID_MARGIN) * 1.5
            desired_vel = pygame.Vector2(self.vel.x, -self.max_speed)
            steer_force_component = (desired_vel - self.vel) * scale
            if steer_force_component.length() > self.max_force * scale:
                 steer_force_component.scale_to_length(self.max_force * scale)
            steer_accumulator += steer_force_component
        
        return steer_accumulator

    def _avoid_collisions(self, all_vehicles):
        total_avoid_force = pygame.Vector2(0, 0)
        for other in all_vehicles:
            if other is self: continue
            dist_vec = self.pos - other.pos
            dist = dist_vec.length()
            safe_dist = self.radius + other.radius + COLLISION_BUFFER
            if 0 < dist < safe_dist: 
                strength = self.max_force * ((safe_dist - dist) / safe_dist) 
                repulsion = dist_vec.normalize() * strength
                total_avoid_force += repulsion
        
        if total_avoid_force.length() > self.max_force: 
             total_avoid_force.scale_to_length(self.max_force)
        return total_avoid_force

    def _apply_general_boundary_constraints(self):
        turn_force_accumulation = pygame.Vector2(0,0)
        min_x_bound, max_x_bound = MAP_MARGIN + self.radius, SCREEN_WIDTH - MAP_MARGIN - self.radius
        min_y_bound, max_y_bound = MAP_MARGIN + self.radius, SCREEN_HEIGHT - MAP_MARGIN - self.radius
        boundary_turn_margin = self.radius * 2.5 # Increase margin for general boundary turn

        if self.pos.x < min_x_bound + boundary_turn_margin:
            desired_vel_change = pygame.Vector2(self.max_speed, self.vel.y) - self.vel
            # Apply stronger, more direct force for general boundaries
            turn_force_accumulation += desired_vel_change.normalize() * self.max_force * BOUNDARY_REPEL_STRENGTH 
        elif self.pos.x > max_x_bound - boundary_turn_margin:
            desired_vel_change = pygame.Vector2(-self.max_speed, self.vel.y) - self.vel
            turn_force_accumulation += desired_vel_change.normalize() * self.max_force * BOUNDARY_REPEL_STRENGTH
        
        if self.pos.y < min_y_bound + boundary_turn_margin:
            desired_vel_change = pygame.Vector2(self.vel.x, self.max_speed) - self.vel
            turn_force_accumulation += desired_vel_change.normalize() * self.max_force * BOUNDARY_REPEL_STRENGTH
        elif self.pos.y > max_y_bound - boundary_turn_margin:
            desired_vel_change = pygame.Vector2(self.vel.x, -self.max_speed) - self.vel
            turn_force_accumulation += desired_vel_change.normalize() * self.max_force * BOUNDARY_REPEL_STRENGTH
        
        if turn_force_accumulation.length_squared() > 0:
            # Cap the accumulated boundary force to prevent excessive force from this behavior alone
            if turn_force_accumulation.length() > self.max_force * BOUNDARY_REPEL_STRENGTH : # Allow it to be stronger than base max_force
                 turn_force_accumulation.scale_to_length(self.max_force * BOUNDARY_REPEL_STRENGTH)
            self.apply_force(turn_force_accumulation)

    def update(self, dt, all_vehicles, formation_target_pos=None):
        behavior_forces = pygame.Vector2(0,0)
        if self.role == 'evader':
            wander_force = self._wander(dt)
            boundary_evade_force = self._avoid_map_boundaries_evader()
            behavior_forces += wander_force
            behavior_forces += boundary_evade_force 
        elif self.role == 'pursuer':
            if formation_target_pos:
                seek_force = self._seek(formation_target_pos)
                behavior_forces += seek_force
        
        avoid_force = self._avoid_collisions(all_vehicles)
        total_steering_force = behavior_forces + (avoid_force * AVOIDANCE_WEIGHT)
        
        # The total steering force (combination of seek/wander and avoidance) is capped by self.max_force
        if total_steering_force.length() > self.max_force:
            total_steering_force.scale_to_length(self.max_force)

        self.apply_force(total_steering_force) # Apply primary steering forces

        # General boundary constraints are applied as an additional, potentially overriding force
        self._apply_general_boundary_constraints() 

        self.vel += self.acc * dt 
        if self.vel.length() > self.max_speed:
            self.vel.scale_to_length(self.max_speed)
        self.pos += self.vel * dt
        self.acc = pygame.Vector2(0, 0) # Reset acceleration

    def draw(self, screen):
        pygame.draw.circle(screen, self.color, (int(self.pos.x), int(self.pos.y)), self.radius)
        pygame.draw.circle(screen, BLACK, (int(self.pos.x), int(self.pos.y)), self.radius, 2)
        if self.vel.length_squared() > 0:
            end_pos = self.pos + self.vel.normalize() * self.radius * 1.5
            pygame.draw.line(screen, BLACK, self.pos, end_pos, 2)

def calculate_formation_targets(evader_pos, num_pursuers, formation_side_length):
    targets = []
    half_side = formation_side_length / 2
    relative_offsets = [
        pygame.Vector2(-half_side, -half_side), pygame.Vector2( half_side, -half_side),
        pygame.Vector2( half_side,  half_side), pygame.Vector2(-half_side,  half_side)
    ]
    for i in range(num_pursuers):
        if i < len(relative_offsets): targets.append(evader_pos + relative_offsets[i])
        else:
            angle = 2 * math.pi * i / num_pursuers
            offset = pygame.Vector2(math.cos(angle), math.sin(angle)) * formation_side_length * 0.707 # sqrt(2)/2 for diagonal
            targets.append(evader_pos + offset)
    return targets

def check_capture(pursuers, evader, formation_side_length, tolerance, capture_radius_threshold, max_angular_gap, escape_gap_factor):
    if len(pursuers) != 4: return False

    centroid_x = sum(p.pos.x for p in pursuers) / 4
    centroid_y = sum(p.pos.y for p in pursuers) / 4
    pursuer_centroid = pygame.Vector2(centroid_x, centroid_y)

    # 1. Evader must be very close to the pursuer centroid. This is a strong indicator of being surrounded.
    if evader.pos.distance_to(pursuer_centroid) > capture_radius_threshold * 0.8: # Stricter: evader must be well within the effective radius
        # print(f"Debug: Evader too far from pursuer centroid. Dist: {evader.pos.distance_to(pursuer_centroid):.1f}, Threshold: {capture_radius_threshold * 0.8:.1f}")
        return False

    # 2. Sort pursuers by angle
    pursuer_angles = [{'p': p, 'angle': math.atan2(p.pos.y - pursuer_centroid.y, p.pos.x - pursuer_centroid.x)} for p in pursuers]
    sorted_pursuers_with_angles = sorted(pursuer_angles, key=lambda item: item['angle'])
    sorted_pursuers = [item['p'] for item in sorted_pursuers_with_angles]

    # 3. Check formation regularity (side lengths) - important for "square" shape
    side_lengths = []
    for i in range(4):
        p1_pos = sorted_pursuers[i].pos
        p2_pos = sorted_pursuers[(i + 1) % 4].pos
        side_lengths.append(p1_pos.distance_to(p2_pos))
    avg_side_length = sum(side_lengths) / 4

    # Allow formations tighter than target, but not excessively large or collapsed.
    # The lower bound (e.g., 0.4 * target) prevents a "point" capture.
    # The upper bound ensures it's not too loose.
    if not (formation_side_length * 0.4 < avg_side_length < formation_side_length + tolerance * 1.8):
        # print(f"Debug: Avg side length {avg_side_length:.1f} out of acceptable range for target {formation_side_length:.1f}")
        return False
    for length in side_lengths: 
        if not (avg_side_length - tolerance * 2.2 < length < avg_side_length + tolerance * 2.2): # Slightly more tolerance for individual sides if avg is good
            # print(f"Debug: Individual side length {length:.1f} too irregular around avg {avg_side_length:.1f}")
            return False

    # 4. Evader within pursuer bounding box (remains a useful check)
    min_x_pursuers = min(p.pos.x for p in pursuers) - evader.radius
    max_x_pursuers = max(p.pos.x for p in pursuers) + evader.radius
    min_y_pursuers = min(p.pos.y for p in pursuers) - evader.radius
    max_y_pursuers = max(p.pos.y for p in pursuers) + evader.radius
    if not (min_x_pursuers < evader.pos.x < max_x_pursuers and \
            min_y_pursuers < evader.pos.y < max_y_pursuers):
        return False

    # 5. Pursuer formation tightness: Average distance of pursuers from their centroid.
    # This is a key measure of the "包围圈内 (设定阈值)".
    avg_dist_to_centroid = sum(p.pos.distance_to(pursuer_centroid) for p in pursuers) / 4
    if avg_dist_to_centroid > capture_radius_threshold:
        # print(f"Debug: Avg dist to pursuer centroid {avg_dist_to_centroid:.1f} > threshold {capture_radius_threshold:.1f}")
        return False

    # 6. Angular distribution of pursuers (ensures 4-sidedness)
    for i in range(4):
        angle1 = sorted_pursuers_with_angles[i]['angle']
        angle2 = sorted_pursuers_with_angles[(i + 1) % 4]['angle']
        angular_diff = angle2 - angle1
        if angular_diff < 0: angular_diff += 2 * math.pi 
        if angular_diff > max_angular_gap:
            # print(f"Debug: Angular gap {math.degrees(angular_diff):.1f} > max allowed {math.degrees(max_angular_gap):.1f}")
            return False
            
    # 7. No Escape Path: Critical check for actual encirclement.
    min_escape_distance = evader.radius * escape_gap_factor
    for i in range(4):
        p1 = sorted_pursuers[i]
        p2 = sorted_pursuers[(i + 1) % 4]
        # Calculate distance between the *edges* of the two pursuers
        gap_distance = p1.pos.distance_to(p2.pos) - p1.radius - p2.radius 
        if gap_distance > min_escape_distance:
            # print(f"Debug: Escape gap detected between pursuers {p1.id} and {p2.id}. Gap: {gap_distance:.1f}, MinEscapeThreshold: {min_escape_distance:.1f}")
            return False 

    print(f"捕获条件满足: 被追捕者在质心附近, 追捕者形成紧密且均匀的四方包围, 无明显逃逸路径。")
    return True


def resolve_hard_vehicle_collisions(all_vehicles):
    for _ in range(5): 
        collisions_found = False
        for i in range(len(all_vehicles)):
            for j in range(i + 1, len(all_vehicles)):
                v1 = all_vehicles[i]
                v2 = all_vehicles[j]
                dist_vec = v1.pos - v2.pos
                dist_sq = dist_vec.length_squared()
                min_dist = v1.radius + v2.radius
                if dist_sq > 0.001 and dist_sq < min_dist * min_dist: 
                    collisions_found = True
                    dist = math.sqrt(dist_sq)
                    overlap = (min_dist - dist) * 0.51 # Ensure they are pushed apart
                    
                    # Correction vector points from v2 to v1 if v1.pos - v2.pos
                    correction_vec = dist_vec.normalize() * overlap 
                    v1.pos += correction_vec
                    v2.pos -= correction_vec # Move in opposite direction
        if not collisions_found: break

def resolve_hard_boundary_collisions(all_vehicles):
    for v in all_vehicles:
        v.pos.x = max(MAP_MARGIN + v.radius, min(v.pos.x, SCREEN_WIDTH - MAP_MARGIN - v.radius))
        v.pos.y = max(MAP_MARGIN + v.radius, min(v.pos.y, SCREEN_HEIGHT - MAP_MARGIN - v.radius))

def draw_grid(screen):
    for x in range(MAP_MARGIN, SCREEN_WIDTH - MAP_MARGIN + 1, 50):
        pygame.draw.line(screen, GRID_COLOR, (x, MAP_MARGIN), (x, SCREEN_HEIGHT - MAP_MARGIN))
    for y in range(MAP_MARGIN, SCREEN_HEIGHT - MAP_MARGIN + 1, 50):
        pygame.draw.line(screen, GRID_COLOR, (MAP_MARGIN, y), (SCREEN_WIDTH - MAP_MARGIN, y))
    pygame.draw.rect(screen, BLACK, (MAP_MARGIN, MAP_MARGIN, MAP_WIDTH, MAP_HEIGHT), 2)

def main():
    pygame.init()
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("UAV 一致性编队与围捕仿真 (优化)")
    clock = pygame.time.Clock()
    font = pygame.font.SysFont("simhei", 30) # Make sure this font is available or use a fallback
    small_font = pygame.font.SysFont("simhei", 22)

    pursuers_start_positions = [
        (VEHICLE_RADIUS * 2, VEHICLE_RADIUS * 2),
        (MAP_WIDTH - VEHICLE_RADIUS * 2, VEHICLE_RADIUS * 2),
        (MAP_WIDTH - VEHICLE_RADIUS * 2, MAP_HEIGHT - VEHICLE_RADIUS * 2),
        (VEHICLE_RADIUS * 2, MAP_HEIGHT - VEHICLE_RADIUS * 2)
    ]
    pursuers = []
    for i in range(4):
        x, y = pursuers_start_positions[i]
        p = Vehicle(x, y, 'pursuer', i, UNIFIED_PURSUER_COLOR, 
                    VEHICLE_RADIUS, PURSUER_MAX_SPEED, PURSUER_MAX_FORCE)
        pursuers.append(p)

    evader = Vehicle(MAP_WIDTH / 2, MAP_HEIGHT / 2, 'evader', -1, EVADER_COLOR, 
                     VEHICLE_RADIUS, EVADER_MAX_SPEED, EVADER_MAX_FORCE)
    all_vehicles = pursuers + [evader]
    
    running = True
    captured = False
    paused = False

    while running:
        dt = clock.tick(FPS) / 1000.0 
        dt = min(dt, 0.05) # Cap dt for stability

        for event in pygame.event.get():
            if event.type == pygame.QUIT: running = False
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE: paused = not paused
                if event.key == pygame.K_r: main(); return # Restart simulation

        if paused:
            pause_text = font.render("已暂停 (空格: 继续 | R: 重置)", True, BLACK)
            screen.blit(pause_text, (SCREEN_WIDTH // 2 - pause_text.get_width() // 2, SCREEN_HEIGHT // 2 - pause_text.get_height() // 2))
            pygame.display.flip()
            continue

        if not captured:
            evader.update(dt, all_vehicles)
            formation_targets = calculate_formation_targets(evader.pos, len(pursuers), FORMATION_SIDE_LENGTH)
            for i, p_obj in enumerate(pursuers):
                p_obj.update(dt, all_vehicles, formation_targets[i])
            
            resolve_hard_vehicle_collisions(all_vehicles) 
            resolve_hard_boundary_collisions(all_vehicles) 

            if check_capture(pursuers, evader, FORMATION_SIDE_LENGTH, CAPTURE_TOLERANCE, 
                             EFFECTIVE_CAPTURE_RADIUS_THRESHOLD, MAX_ALLOWED_ANGULAR_GAP,
                             MIN_ESCAPE_GAP_FACTOR): 
                captured = True
                print("目标已捕获!")
        
        screen.fill(WHITE)
        draw_grid(screen)
        for v_obj in all_vehicles: v_obj.draw(screen)
        
        status_text = "状态: " + ("目标已捕获!" if captured else "追捕中...")
        status_surface = font.render(status_text, True, GREEN if captured else BLACK)
        screen.blit(status_surface, (MAP_MARGIN + 10, MAP_MARGIN - 35 if MAP_MARGIN > 35 else 10))
        
        instructions_text = small_font.render("空格: 暂停/继续 | R: 重置", True, BLACK)
        screen.blit(instructions_text, (MAP_MARGIN + 10, SCREEN_HEIGHT - MAP_MARGIN + 10))

        pygame.display.flip()

    pygame.quit()

if __name__ == '__main__':
    main()
