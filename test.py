import pygame
import numpy as np
import random
import math

# --- Constants ---
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 800
MAP_SIZE = 800

UAV_RADIUS = 10
ENEMY_RADIUS = 12
FRIENDLY_UAV_MAX_SPEED = 130
ENEMY_UAV_MAX_SPEED = 105

# Formation and Capture Parameters
TARGET_ROUNDUP_RADIUS_AROUND_ENEMY = ENEMY_RADIUS + UAV_RADIUS + ENEMY_RADIUS * 0.8 # 26.2
INITIAL_FORMATION_RADIUS_FACTOR = 4.0
FORMATION_SHRINK_RATE_PER_SEC_FACTOR = 0.55
IDEAL_CAPTURE_FORMATION_SIDE_LENGTH = TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * math.sqrt(2) # Approx 37.05

# --- ADJUSTED SUCCESS CONDITION PARAMETERS (v4.5) ---
MAX_ACTUAL_ENCIRCLEMENT_RADIUS_DEVIATION = UAV_RADIUS * 2.0 # Was 1.25. Much more lenient.
FORMATION_SPAN_TOLERANCE = UAV_RADIUS * 4.0  # Was 2.5. Much more lenient for shape.
MAX_ENEMY_OFFSET_FROM_FORMATION_CENTER = ENEMY_RADIUS * 2.5 # Was 1.75. More lenient.
ENEMY_TRAPPED_VELOCITY_THRESHOLD_FACTOR = 0.20 # Was 0.12. Much more lenient on enemy speed.

# Collision Avoidance for Friendly UAVs (v4.8) - Reduced for tighter formation
FRIENDLY_COLLISION_AVOIDANCE_DIST = UAV_RADIUS * 2.2  # Reduced from 3.0 - allow closer proximity
FRIENDLY_COLLISION_REPULSION_STRENGTH = 2.5  # Reduced from 3.8 - less aggressive
FRIENDLY_EXTREME_COLLISION_BOOST = 1.8  # Reduced from 2.5

# Friendly-Enemy Collision Avoidance Parameters (v4.7) - FIXED: Collision distance must be < target radius!
FRIENDLY_ENEMY_COLLISION_AVOIDANCE_DIST = UAV_RADIUS + ENEMY_RADIUS + UAV_RADIUS * 0.1 # 24.0 - MUCH smaller than target radius (28)
FRIENDLY_ENEMY_COLLISION_REPULSION_STRENGTH = 1.8 # Reduced from 2.7 - less aggressive repulsion
FRIENDLY_ENEMY_REPULSION_WEIGHT = 0.8 # Reduced from 1.6 - much lower priority

# Map Edge Avoidance (v4.5) - Significantly improved enemy edge avoidance
MAP_EDGE_AVOIDANCE_DIST_FRIENDLY = UAV_RADIUS * 4.0 # Was 3.5. Friendlies even more aware of edges.
MAP_EDGE_REPULSION_STRENGTH_FRIENDLY = 1.0 # Was 0.9. Stronger push from edge for friendlies.
MAP_EDGE_AVOIDANCE_DIST_ENEMY = UAV_RADIUS * 4.0 # Was 1.8. Enemy much more edge aware.
MAP_EDGE_REPULSION_STRENGTH_ENEMY = 0.85 # Was 0.30. Much stronger push for enemy from edge.

# Enemy Behavior (v4.8) - Further reduced escape behavior
ENEMY_WANDER_DIRECTION_CHANGE_INTERVAL = 1.2
ENEMY_EVASION_RADIUS_FROM_FRIENDLY = UAV_RADIUS * 6.5
ENEMY_EVASION_STRENGTH = 0.6 # Reduced from 1.0 - much less aggressive escape
ENEMY_MIN_VELOCITY_KICK_FACTOR = 0.40
# New parameters for center-seeking behavior when near edges
ENEMY_CENTER_SEEK_THRESHOLD = UAV_RADIUS * 8.0 # Increased from 6.0 - start seeking center earlier
ENEMY_CENTER_SEEK_STRENGTH = 1.0 # Increased from 0.6 - stronger center-seeking force

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (220, 50, 50)
GREEN = (50, 205, 50)
DARK_GREEN_SUCCESS = (0, 100, 0)
GRAY = (200, 200, 200)

# Control Behavior Factors
FRIENDLY_UAV_STEERING_INTEGRATION_FACTOR = 0.15
FRIENDLY_ARRIVAL_RADIUS_FACTOR = 0.50
FRIENDLY_MIN_ARRIVAL_SPEED_FACTOR = 0.20

# Behavior Weights for Friendly UAVs (v4.7) - Rebalanced for better formation control
FORMATION_WEIGHT = 1.2 # Increased from 0.70 - formation has higher priority to overcome collision avoidance
FRIENDLY_REPULSION_WEIGHT = 2.5
EDGE_REPULSION_WEIGHT_FRIENDLY = 1.0 # Was 0.9. Higher priority to avoid edges.

# --- NEW: Containment Squeeze Parameters (v4.4) ---
CONTAINMENT_SQUEEZE_STRENGTH_FACTOR = 0.08 # Strength of the squeeze force relative to max_speed
CONTAINMENT_SQUEEZE_WEIGHT = 0.25      # Weight of the squeeze force in total steering


# --- Helper Functions ---
def limit_vector(vector, max_length):
    length = np.linalg.norm(vector)
    if length > max_length and length > 0:
        return (vector / length) * max_length
    return vector

def get_map_edge_repulsion(pos, radius, max_speed, map_s, avoidance_dist, repulsion_strength):
    repulsion = np.array([0.0, 0.0])
    if avoidance_dist <= 0: return repulsion
    dist_to_left_edge = pos[0] - radius
    if dist_to_left_edge < avoidance_dist:
        strength = (avoidance_dist - dist_to_left_edge) / avoidance_dist
        repulsion[0] += strength * max_speed * repulsion_strength
    dist_to_right_edge = map_s - (pos[0] + radius)
    if dist_to_right_edge < avoidance_dist:
        strength = (avoidance_dist - dist_to_right_edge) / avoidance_dist
        repulsion[0] -= strength * max_speed * repulsion_strength
    dist_to_top_edge = pos[1] - radius
    if dist_to_top_edge < avoidance_dist:
        strength = (avoidance_dist - dist_to_top_edge) / avoidance_dist
        repulsion[1] += strength * max_speed * repulsion_strength
    dist_to_bottom_edge = map_s - (pos[1] + radius)
    if dist_to_bottom_edge < avoidance_dist:
        strength = (avoidance_dist - dist_to_bottom_edge) / avoidance_dist
        repulsion[1] -= strength * max_speed * repulsion_strength
    return repulsion

# --- UAV Base Class ---
class UAV:
    def __init__(self, x, y, radius, color, max_speed, label=""):
        self.pos = np.array([x, y], dtype=float)
        self.vel = np.array([0, 0], dtype=float)
        self.radius = radius
        self.color = color
        self.max_speed = max_speed
        self.label = label

    def update_physics(self, dt):
        self.pos += self.vel * dt
        self.pos[0] = np.clip(self.pos[0], self.radius, MAP_SIZE - self.radius)
        self.pos[1] = np.clip(self.pos[1], self.radius, MAP_SIZE - self.radius)

    def draw(self, screen, font=None):
        pygame.draw.circle(screen, self.color, self.pos.astype(int), self.radius)
        pygame.draw.circle(screen, BLACK, self.pos.astype(int), self.radius, 1)
        if font and self.label:
            text_surf = font.render(self.label, True, BLACK)
            text_rect = text_surf.get_rect(center=self.pos.astype(int))
            screen.blit(text_surf, text_rect)

# --- FriendlyUAV Class ---
class FriendlyUAV(UAV):
    def __init__(self, x, y, uav_id):
        super().__init__(x, y, UAV_RADIUS, GREEN, FRIENDLY_UAV_MAX_SPEED, label=f"F{uav_id}")
        self.id = uav_id

    def update(self, dt, enemy_uav_obj, friendly_uavs, current_formation_target_radius, friendly_formation_center_calc): # Added friendly_formation_center_calc
        self.compute_control_input(dt, enemy_uav_obj, friendly_uavs, current_formation_target_radius, friendly_formation_center_calc)
        self.update_physics(dt)

    def compute_control_input(self, dt, enemy_uav_obj, friendly_uavs, current_formation_target_radius, friendly_formation_center_calc): # Added friendly_formation_center_calc
        enemy_pos = enemy_uav_obj.pos

        # NEW v4.8: Dynamic target position adjustment for better encirclement
        offset_component = current_formation_target_radius / math.sqrt(2) if current_formation_target_radius > 0 else 0
        base_offsets = [
            np.array([-offset_component, -offset_component]),
            np.array([offset_component, -offset_component]),
            np.array([offset_component, offset_component]),
            np.array([-offset_component, offset_component])
        ]

        # Calculate ideal target position
        ideal_target_pos = enemy_pos + base_offsets[self.id]

        # NEW: Dynamic adjustment based on other UAVs' positions for better distribution
        if current_formation_target_radius <= TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.1:
            # When in tight formation, adjust position to fill gaps
            other_uavs = [uav for uav in friendly_uavs if uav.id != self.id]
            if other_uavs:
                # Find the centroid of other UAVs relative to enemy
                other_positions = np.array([uav.pos for uav in other_uavs])
                other_centroid = np.mean(other_positions, axis=0)

                # Adjust target to be opposite to the centroid for better distribution
                centroid_to_enemy = enemy_pos - other_centroid
                if np.linalg.norm(centroid_to_enemy) > 0.1:
                    centroid_to_enemy_normalized = centroid_to_enemy / np.linalg.norm(centroid_to_enemy)
                    # Blend ideal position with gap-filling position
                    gap_fill_pos = enemy_pos + centroid_to_enemy_normalized * current_formation_target_radius
                    ideal_target_pos = 0.7 * ideal_target_pos + 0.3 * gap_fill_pos

        target_pos = ideal_target_pos

        safe_margin_from_edge = self.radius * 5.0 # Was 4.0. Increased margin even more significantly.
        target_pos[0] = np.clip(target_pos[0], safe_margin_from_edge, MAP_SIZE - safe_margin_from_edge)
        target_pos[1] = np.clip(target_pos[1], safe_margin_from_edge, MAP_SIZE - safe_margin_from_edge)

        vec_to_target = target_pos - self.pos
        dist_to_target = np.linalg.norm(vec_to_target)
        desired_velocity_formation = np.array([0.0,0.0])
        if dist_to_target > 0.1:
            arrival_radius = self.max_speed * FRIENDLY_ARRIVAL_RADIUS_FACTOR
            min_speed = self.max_speed * FRIENDLY_MIN_ARRIVAL_SPEED_FACTOR
            if dist_to_target < arrival_radius:
                speed = self.max_speed * (dist_to_target / arrival_radius if arrival_radius > 0 else 1.0)
                speed = max(speed, min_speed)
            else:
                speed = self.max_speed
            if dist_to_target > 0:
                desired_velocity_formation = (vec_to_target / dist_to_target) * speed
        steering_force_formation = desired_velocity_formation - self.vel

        repulsion_sum_friendly = np.array([0.0, 0.0])
        for other_uav in friendly_uavs:
            if other_uav.id == self.id:
                continue
            dist_vec = self.pos - other_uav.pos
            distance = np.linalg.norm(dist_vec)
            if 0 < distance < FRIENDLY_COLLISION_AVOIDANCE_DIST:
                strength_factor = (FRIENDLY_COLLISION_AVOIDANCE_DIST - distance) / FRIENDLY_COLLISION_AVOIDANCE_DIST if FRIENDLY_COLLISION_AVOIDANCE_DIST > 0 else 1.0
                repel_force_magnitude = strength_factor * self.max_speed * FRIENDLY_COLLISION_REPULSION_STRENGTH
                if distance < self.radius * 1.5:
                    repel_force_magnitude *= FRIENDLY_EXTREME_COLLISION_BOOST
                if distance > 0:
                    repulsion_sum_friendly += (dist_vec / distance) * repel_force_magnitude

        edge_repulsion_force = get_map_edge_repulsion(self.pos, self.radius, self.max_speed, MAP_SIZE,
                                                      MAP_EDGE_AVOIDANCE_DIST_FRIENDLY,
                                                      MAP_EDGE_REPULSION_STRENGTH_FRIENDLY)

        repulsion_from_enemy = np.array([0.0, 0.0])
        dist_vec_to_enemy = self.pos - enemy_pos
        dist_to_enemy = np.linalg.norm(dist_vec_to_enemy)
        if 0 < dist_to_enemy < FRIENDLY_ENEMY_COLLISION_AVOIDANCE_DIST:
            strength_factor = (FRIENDLY_ENEMY_COLLISION_AVOIDANCE_DIST - dist_to_enemy) / FRIENDLY_ENEMY_COLLISION_AVOIDANCE_DIST if FRIENDLY_ENEMY_COLLISION_AVOIDANCE_DIST > 0 else 1.0
            repel_force_magnitude = strength_factor * self.max_speed * FRIENDLY_ENEMY_COLLISION_REPULSION_STRENGTH
            if dist_to_enemy > 0 :
                 repulsion_from_enemy = (dist_vec_to_enemy / dist_to_enemy) * repel_force_magnitude

        # --- NEW: Containment Squeeze Force (v4.4) ---
        squeeze_force_component = np.array([0.0, 0.0])
        if current_formation_target_radius <= TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.05: # If formation is meant to be tight
            if friendly_formation_center_calc is not None: # Ensure center is calculated and passed
                vec_to_friendly_center = friendly_formation_center_calc - self.pos
                dist_to_friendly_center = np.linalg.norm(vec_to_friendly_center)
                if dist_to_friendly_center > self.radius * 0.5: # Avoid extreme force if already at center
                    # Apply a force towards the center of the friendly formation
                    squeeze_force_component = (vec_to_friendly_center / dist_to_friendly_center) * self.max_speed * CONTAINMENT_SQUEEZE_STRENGTH_FACTOR


        total_steering_influence = (steering_force_formation * FORMATION_WEIGHT +
                                    repulsion_sum_friendly * FRIENDLY_REPULSION_WEIGHT +
                                    edge_repulsion_force * EDGE_REPULSION_WEIGHT_FRIENDLY +
                                    repulsion_from_enemy * FRIENDLY_ENEMY_REPULSION_WEIGHT +
                                    squeeze_force_component * CONTAINMENT_SQUEEZE_WEIGHT) # Added squeeze force

        effective_dt_factor = dt / (1/60.0) if (1/60.0) > 0 else 1.0
        self.vel += total_steering_influence * FRIENDLY_UAV_STEERING_INTEGRATION_FACTOR * effective_dt_factor
        self.vel = limit_vector(self.vel, self.max_speed)

# --- EnemyUAV Class ---
class EnemyUAV(UAV):
    def __init__(self, x, y):
        super().__init__(x, y, ENEMY_RADIUS, RED, ENEMY_UAV_MAX_SPEED, label="E")
        self.wander_angle = random.uniform(0, 2 * math.pi)
        self.time_to_change_wander_direction = 0

    def update(self, dt, friendly_uavs):
        self.update_behavior(dt, friendly_uavs)
        self.update_physics(dt)

    def update_behavior(self, dt, friendly_uavs):
        self.time_to_change_wander_direction -= dt
        if self.time_to_change_wander_direction <= 0:
            self.wander_angle += random.uniform(-math.pi / 1.5, math.pi / 1.5)
            self.time_to_change_wander_direction = ENEMY_WANDER_DIRECTION_CHANGE_INTERVAL + random.uniform(-0.15,0.15)
        wander_velocity = np.array([math.cos(self.wander_angle), math.sin(self.wander_angle)]) * self.max_speed * 0.55

        evasion_influence = np.array([0.0, 0.0])
        avg_nearby_friendly_pos = np.array([0.0, 0.0])
        num_nearby_friendlies = 0
        closest_friendly_dist = float('inf')

        for fuav in friendly_uavs:
            dist_vec = self.pos - fuav.pos
            dist_to_friendly = np.linalg.norm(dist_vec)
            closest_friendly_dist = min(closest_friendly_dist, dist_to_friendly)
            if dist_to_friendly < ENEMY_EVASION_RADIUS_FROM_FRIENDLY:
                avg_nearby_friendly_pos += fuav.pos
                num_nearby_friendlies += 1

        if num_nearby_friendlies > 0:
            avg_nearby_friendly_pos /= num_nearby_friendlies
            direction_from_avg_threat = self.pos - avg_nearby_friendly_pos
            dist_from_avg_threat = np.linalg.norm(direction_from_avg_threat)
            if dist_from_avg_threat > 0.1:
                normalized_evasion_dir = direction_from_avg_threat / dist_from_avg_threat
                evasion_strength_factor = (ENEMY_EVASION_RADIUS_FROM_FRIENDLY - closest_friendly_dist) / ENEMY_EVASION_RADIUS_FROM_FRIENDLY if ENEMY_EVASION_RADIUS_FROM_FRIENDLY > 0 else 0
                evasion_strength_factor = max(0, evasion_strength_factor)
                evasion_influence = normalized_evasion_dir * self.max_speed * evasion_strength_factor * ENEMY_EVASION_STRENGTH

        edge_repulsion_force = get_map_edge_repulsion(self.pos, self.radius, self.max_speed, MAP_SIZE,
                                                      MAP_EDGE_AVOIDANCE_DIST_ENEMY,
                                                      MAP_EDGE_REPULSION_STRENGTH_ENEMY)

        # NEW: Center-seeking behavior when near edges (v4.5)
        center_seek_force = np.array([0.0, 0.0])
        map_center = np.array([MAP_SIZE / 2, MAP_SIZE / 2])

        # Check distance to each edge
        dist_to_left = self.pos[0] - self.radius
        dist_to_right = MAP_SIZE - (self.pos[0] + self.radius)
        dist_to_top = self.pos[1] - self.radius
        dist_to_bottom = MAP_SIZE - (self.pos[1] + self.radius)
        min_edge_dist = min(dist_to_left, dist_to_right, dist_to_top, dist_to_bottom)

        if min_edge_dist < ENEMY_CENTER_SEEK_THRESHOLD:
            # Seek toward map center when near edges
            direction_to_center = map_center - self.pos
            dist_to_center = np.linalg.norm(direction_to_center)
            if dist_to_center > 0.1:
                normalized_center_dir = direction_to_center / dist_to_center
                # Stronger center-seeking when closer to edge
                strength_factor = (ENEMY_CENTER_SEEK_THRESHOLD - min_edge_dist) / ENEMY_CENTER_SEEK_THRESHOLD
                center_seek_force = normalized_center_dir * self.max_speed * ENEMY_CENTER_SEEK_STRENGTH * strength_factor

        # Adjust evasion strength based on proximity to edges (v4.8) - More aggressive reduction
        edge_proximity_factor = 1.0
        if min_edge_dist < ENEMY_CENTER_SEEK_THRESHOLD:
            # Much more aggressive reduction of evasion when near edges
            edge_proximity_factor = min_edge_dist / ENEMY_CENTER_SEEK_THRESHOLD
            edge_proximity_factor = max(0.1, edge_proximity_factor)  # Reduced from 0.3 to 0.1

        # Apply adjusted evasion influence
        adjusted_evasion_influence = evasion_influence * edge_proximity_factor

        # Combine all forces with improved weighting (v4.8) - Reduced evasion, increased center-seeking
        if np.linalg.norm(adjusted_evasion_influence) > 0.05 * self.max_speed:
            # When evading, much stronger prioritization of edge avoidance and center-seeking
            self.vel = (wander_velocity * 0.05 +
                       adjusted_evasion_influence * 0.35 +  # Reduced from 0.60
                       edge_repulsion_force * 0.60 +        # Increased from 0.50
                       center_seek_force * 0.50)            # Increased from 0.35
        else:
            # When not evading, even stronger prioritization of staying away from edges
            self.vel = (wander_velocity * 0.25 +             # Reduced from 0.35
                       edge_repulsion_force * 0.75 +         # Increased from 0.65
                       center_seek_force * 0.50)             # Increased from 0.30

        self.vel = limit_vector(self.vel, self.max_speed)

        if np.linalg.norm(self.vel) < self.max_speed * 0.05 and dt > 0:
            kick_angle_offset = random.uniform(-math.pi/3, math.pi/3)
            kick_direction = np.array([math.cos(self.wander_angle + kick_angle_offset), math.sin(self.wander_angle + kick_angle_offset)])
            kick_velocity = kick_direction * self.max_speed * ENEMY_MIN_VELOCITY_KICK_FACTOR
            self.vel += kick_velocity
            self.vel = limit_vector(self.vel, self.max_speed)


# --- Main Simulation ---
def main():
    pygame.init()
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("Unmanned Vehicle Roundup Simulation v4.8 (Improved Formation & Reduced Enemy Escape)")
    clock = pygame.time.Clock()
    small_font = pygame.font.Font(None, 22)
    large_font = pygame.font.Font(None, 60)
    debug_font = pygame.font.Font(None, 18)
    dt = 0

    initial_margin_from_edge = UAV_RADIUS * 15 # Was 12. Start friendlies even further from edge
    friendly_uavs = [
        FriendlyUAV(initial_margin_from_edge, initial_margin_from_edge, 0),
        FriendlyUAV(MAP_SIZE - initial_margin_from_edge, initial_margin_from_edge, 1),
        FriendlyUAV(MAP_SIZE - initial_margin_from_edge, MAP_SIZE - initial_margin_from_edge, 2),
        FriendlyUAV(initial_margin_from_edge, MAP_SIZE - initial_margin_from_edge, 3)
    ]
    enemy_uav = EnemyUAV(MAP_SIZE / 2, MAP_SIZE / 2)

    current_formation_radius_around_enemy = TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * INITIAL_FORMATION_RADIUS_FACTOR
    formation_shrink_actual_rate = TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * FORMATION_SHRINK_RATE_PER_SEC_FACTOR

    running = True
    mission_accomplished = False
    time_of_completion = 0
    start_time = pygame.time.get_ticks()

    debug_actual_radius_check = False
    debug_shape_check_angular = False
    debug_enemy_velocity_check = False
    debug_target_radius_minimal_check = False
    debug_actual_avg_radius_from_enemy = 0.0
    debug_max_radius_from_enemy = 0.0
    debug_min_radius_from_enemy = 0.0
    debug_angle_gaps = []
    calculated_friendly_formation_center = None # For squeeze force


    while running:
        current_tick = pygame.time.get_ticks()
        if not mission_accomplished:
            elapsed_time_seconds = (current_tick - start_time) / 1000.0
        else:
            elapsed_time_seconds = time_of_completion

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r:
                    initial_margin_from_edge = UAV_RADIUS * 15  # Ensure consistent margin on reset
                    friendly_uavs = [
                        FriendlyUAV(initial_margin_from_edge, initial_margin_from_edge, 0),
                        FriendlyUAV(MAP_SIZE - initial_margin_from_edge, initial_margin_from_edge, 1),
                        FriendlyUAV(MAP_SIZE - initial_margin_from_edge, MAP_SIZE - initial_margin_from_edge, 2),
                        FriendlyUAV(initial_margin_from_edge, MAP_SIZE - initial_margin_from_edge, 3)
                    ]
                    enemy_uav = EnemyUAV(MAP_SIZE / 2, MAP_SIZE / 2)
                    current_formation_radius_around_enemy = TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * INITIAL_FORMATION_RADIUS_FACTOR
                    mission_accomplished = False
                    time_of_completion = 0
                    start_time = pygame.time.get_ticks()
                    debug_actual_radius_check = False; debug_shape_check_angular = False
                    debug_enemy_velocity_check = False
                    debug_target_radius_minimal_check = False
                    debug_actual_avg_radius_from_enemy = 0.0; debug_max_radius_from_enemy = 0.0
                    debug_min_radius_from_enemy = 0.0; debug_angle_gaps = []
                    calculated_friendly_formation_center = None


        if not mission_accomplished:
            enemy_uav.update(dt, friendly_uavs)

            avg_dist_friendlies_to_enemy = np.mean([np.linalg.norm(f.pos - enemy_uav.pos) for f in friendly_uavs])
            condition_to_shrink = (avg_dist_friendlies_to_enemy < current_formation_radius_around_enemy * 1.7 or \
                                   current_formation_radius_around_enemy > TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.25)

            if condition_to_shrink and current_formation_radius_around_enemy > TARGET_ROUNDUP_RADIUS_AROUND_ENEMY:
                current_formation_radius_around_enemy -= formation_shrink_actual_rate * dt
                current_formation_radius_around_enemy = max(current_formation_radius_around_enemy, TARGET_ROUNDUP_RADIUS_AROUND_ENEMY)
            elif avg_dist_friendlies_to_enemy <= TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.2:
                 current_formation_radius_around_enemy = TARGET_ROUNDUP_RADIUS_AROUND_ENEMY

            # Calculate friendly formation center once per frame for squeeze force and success checks
            if friendly_uavs: # Ensure list is not empty
                friendly_positions_for_center = np.array([f.pos for f in friendly_uavs])
                calculated_friendly_formation_center = np.mean(friendly_positions_for_center, axis=0)
            else:
                calculated_friendly_formation_center = None


            for fuav in friendly_uavs:
                fuav.update(dt, enemy_uav, friendly_uavs, current_formation_radius_around_enemy, calculated_friendly_formation_center)

            # --- NEW Mission Accomplished Checks (v4.6) - Enemy-Centered Logic ---
            target_radius_minimal_check = current_formation_radius_around_enemy <= TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.02
            debug_target_radius_minimal_check = target_radius_minimal_check

            if target_radius_minimal_check and friendly_uavs:
                # NEW: Use enemy position as the center of encirclement (not friendly formation center)
                enemy_center = enemy_uav.pos

                # Calculate distances from each friendly UAV to enemy center
                distances_from_enemy = [np.linalg.norm(f.pos - enemy_center) for f in friendly_uavs]
                actual_avg_radius_from_enemy = np.mean(distances_from_enemy)
                max_radius_from_enemy = max(distances_from_enemy)
                min_radius_from_enemy = min(distances_from_enemy)

                # Check if all friendly UAVs are within the target encirclement radius around enemy
                radius_check = max_radius_from_enemy <= TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.3  # Allow some tolerance
                radius_consistency_check = (max_radius_from_enemy - min_radius_from_enemy) <= TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 0.6  # Check formation consistency

                debug_actual_radius_check = radius_check and radius_consistency_check
                debug_actual_avg_radius_from_enemy = actual_avg_radius_from_enemy
                debug_max_radius_from_enemy = max_radius_from_enemy
                debug_min_radius_from_enemy = min_radius_from_enemy

                # Check angular distribution (square formation around enemy)
                angles = []
                for f in friendly_uavs:
                    dx = f.pos[0] - enemy_center[0]
                    dy = f.pos[1] - enemy_center[1]
                    angle = math.atan2(dy, dx)
                    if angle < 0:
                        angle += 2 * math.pi
                    angles.append(angle)

                # Sort angles and check if they form roughly a square (4 quadrants)
                angles.sort()
                angle_gaps = []
                for i in range(len(angles)):
                    next_angle = angles[(i + 1) % len(angles)]
                    if i == len(angles) - 1:  # Last to first
                        gap = (2 * math.pi - angles[i]) + angles[0]
                    else:
                        gap = next_angle - angles[i]
                    angle_gaps.append(gap)

                # Check if angles are roughly evenly distributed (much more lenient)
                # NEW: Much more lenient angular check - just ensure no huge gaps
                max_gap = max(angle_gaps) if angle_gaps else 0
                min_gap = min(angle_gaps) if angle_gaps else 0
                angular_distribution_check = (max_gap - min_gap) < math.pi  # Allow much larger variation

                debug_shape_check_angular = angular_distribution_check
                debug_angle_gaps = angle_gaps

                # Enemy velocity check (more lenient)
                enemy_velocity_check = np.linalg.norm(enemy_uav.vel) < enemy_uav.max_speed * ENEMY_TRAPPED_VELOCITY_THRESHOLD_FACTOR
                debug_enemy_velocity_check = enemy_velocity_check

                # Success condition: All checks pass
                if debug_actual_radius_check and debug_shape_check_angular and enemy_velocity_check:
                    mission_accomplished = True
                    time_of_completion = (current_tick - start_time) / 1000.0
                    print(f"MISSION ACCOMPLISHED! Enemy surrounded at {time_of_completion:.2f}s.")
                    print(f"  Enemy Center: ({enemy_center[0]:.1f}, {enemy_center[1]:.1f})")
                    print(f"  Avg Distance to Enemy: {actual_avg_radius_from_enemy:.2f}, Max: {max_radius_from_enemy:.2f}, Min: {min_radius_from_enemy:.2f}")
                    print(f"  Target Encirclement Radius: {TARGET_ROUNDUP_RADIUS_AROUND_ENEMY:.2f}")
                    print(f"  Angular Gaps: {[f'{gap*180/math.pi:.1f}°' for gap in angle_gaps]}")
                    print(f"  Enemy Vel: {np.linalg.norm(enemy_uav.vel):.2f} (Max for capture: {enemy_uav.max_speed * ENEMY_TRAPPED_VELOCITY_THRESHOLD_FACTOR:.1f})")
            else:
                debug_actual_radius_check = False; debug_shape_check_angular = False
                debug_enemy_velocity_check = False
                debug_actual_avg_radius_from_enemy = 0.0; debug_max_radius_from_enemy = 0.0
                debug_min_radius_from_enemy = 0.0; debug_angle_gaps = []


        # --- Drawing ---
        screen.fill(GRAY)
        pygame.draw.rect(screen, BLACK, (0, 0, MAP_SIZE, MAP_SIZE), 3)

        # NEW: Draw target encirclement circle around enemy (v4.6)
        if not mission_accomplished:
            # Draw target encirclement radius (blue circle)
            pygame.draw.circle(screen, (100, 100, 255),
                             (int(enemy_uav.pos[0]), int(enemy_uav.pos[1])),
                             int(TARGET_ROUNDUP_RADIUS_AROUND_ENEMY), 2)

            # Draw current formation radius for comparison (light gray circle)
            pygame.draw.circle(screen, (200, 200, 200),
                             (int(enemy_uav.pos[0]), int(enemy_uav.pos[1])),
                             int(current_formation_radius_around_enemy), 1)

        for fuav in friendly_uavs:
            fuav.draw(screen, small_font)
        enemy_uav.draw(screen, small_font)

        info_text_y_start = 10
        line_height_small = 20
        line_height_debug = 15

        info_text_lines = [
            f"Time: {elapsed_time_seconds:.1f}s",
            f"Target Form. R: {current_formation_radius_around_enemy:.1f}",
        ]
        if not mission_accomplished:
            info_text_lines.append(f"Enemy Vel: ({enemy_uav.vel[0]:.0f}, {enemy_uav.vel[1]:.0f}) L: {np.linalg.norm(enemy_uav.vel):.0f}")
        else:
             info_text_lines.append(f"Enemy Vel (capture): ({enemy_uav.vel[0]:.0f}, {enemy_uav.vel[1]:.0f}) L: {np.linalg.norm(enemy_uav.vel):.0f}")

        for i, line in enumerate(info_text_lines):
            info_surf = small_font.render(line, True, BLACK)
            screen.blit(info_surf, (10, info_text_y_start + i * line_height_small ))

        current_y_offset = info_text_y_start + len(info_text_lines) * line_height_small + 5

        if not mission_accomplished:
            debug_lines = [
                "--- Success Criteria (Enemy-Centered) ---",
                f"Target R Min: {debug_target_radius_minimal_check}",
                f"Radius OK: {debug_actual_radius_check} (Avg: {debug_actual_avg_radius_from_enemy:.1f}, Max: {debug_max_radius_from_enemy:.1f}, Min: {debug_min_radius_from_enemy:.1f})",
                f"Target Radius: {TARGET_ROUNDUP_RADIUS_AROUND_ENEMY:.1f}",
                f"Angular OK: {debug_shape_check_angular} (Gaps: {[f'{gap*180/math.pi:.0f}°' for gap in debug_angle_gaps] if debug_angle_gaps else 'N/A'})",
                f"Enemy Slow: {debug_enemy_velocity_check} (Vel: {np.linalg.norm(enemy_uav.vel):.1f}, Max: {enemy_uav.max_speed * ENEMY_TRAPPED_VELOCITY_THRESHOLD_FACTOR:.1f})",
            ]
            for i, line in enumerate(debug_lines):
                debug_surf = debug_font.render(line, True, BLACK)
                screen.blit(debug_surf, (10, current_y_offset + i * line_height_debug))

        if mission_accomplished:
            text_surface = large_font.render("MISSION ACCOMPLISHED!", True, DARK_GREEN_SUCCESS)
            text_rect = text_surface.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 40))
            screen.blit(text_surface, text_rect)

            time_taken_surface = small_font.render(f"Time taken: {time_of_completion:.2f} seconds", True, BLACK)
            time_taken_rect = time_taken_surface.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 10))
            screen.blit(time_taken_surface, time_taken_rect)

            restart_text_surface = small_font.render("Press 'R' to Restart", True, BLACK)
            restart_text_rect = restart_text_surface.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 40))
            screen.blit(restart_text_surface, restart_text_rect)

        pygame.display.flip()

        dt = clock.tick(60) / 1000.0
        dt = max(0.0001, min(dt, 0.05))

    pygame.quit()

if __name__ == '__main__':
    main()
