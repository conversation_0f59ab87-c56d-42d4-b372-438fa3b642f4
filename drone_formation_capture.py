import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle, Rectangle
import math
import time

class Vehicle:
    """Vehicle class"""
    def __init__(self, x, y, vehicle_id, is_target=False):
        self.x = x
        self.y = y
        self.vx = 0  # x velocity
        self.vy = 0  # y velocity
        self.vehicle_id = vehicle_id
        self.is_target = is_target  # whether it's target vehicle
        self.radius = 0.3  # vehicle radius
        # Set speed parameters based on vehicle type
        if is_target:
            self.max_speed = 0.5  # target vehicle max speed
            self.max_acceleration = 0.3  # target vehicle max acceleration
        else:
            self.max_speed = 0.8  # pursuer vehicle max speed
            self.max_acceleration = 0.4  # pursuer vehicle max acceleration
        self.communication_range = 5.0  # communication range
        self.safety_distance = 0.8  # safety distance
        
    def update_position(self, dt):
        """Update position"""
        # Limit speed
        speed = math.sqrt(self.vx**2 + self.vy**2)
        if speed > self.max_speed:
            self.vx = self.vx / speed * self.max_speed
            self.vy = self.vy / speed * self.max_speed
            
        # Update position
        self.x += self.vx * dt
        self.y += self.vy * dt
        
    def distance_to(self, other):
        """Calculate distance to another vehicle"""
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)
    
    def is_collision(self, other):
        """Check if collision occurs"""
        return self.distance_to(other) < (self.radius + other.radius)

class FormationController:
    """Formation Controller"""
    def __init__(self, vehicles, target_vehicle, map_size=10):
        self.vehicles = vehicles  # List of pursuing vehicles
        self.target = target_vehicle  # Target vehicle
        self.map_size = map_size
        self.formation_radius = 2.0  # Formation radius
        self.k_formation = 1.0  # Formation control gain
        self.k_consensus = 0.8  # Consensus control gain
        self.k_avoidance = 2.0  # Avoidance control gain
        self.k_boundary = 1.5  # Boundary control gain
        
    def get_desired_position(self, vehicle_id):
        """Get desired position for a vehicle in formation"""
        angles = [0, math.pi/2, math.pi, 3*math.pi/2]  # Four directions
        angle = angles[vehicle_id]
        
        desired_x = self.target.x + self.formation_radius * math.cos(angle)
        desired_y = self.target.y + self.formation_radius * math.sin(angle)
        
        return desired_x, desired_y
    
    def formation_control(self, vehicle):
        """编队控制力"""
        desired_x, desired_y = self.get_desired_position(vehicle.vehicle_id)
        
        fx = self.k_formation * (desired_x - vehicle.x)
        fy = self.k_formation * (desired_y - vehicle.y)
        
        return fx, fy
    
    def consensus_control(self, vehicle):
        """一致性控制力"""
        fx, fy = 0, 0
        neighbor_count = 0
        
        for other in self.vehicles:
            if other.vehicle_id != vehicle.vehicle_id:
                distance = vehicle.distance_to(other)
                if distance < vehicle.communication_range:
                    # 速度一致性
                    fx += self.k_consensus * (other.vx - vehicle.vx)
                    fy += self.k_consensus * (other.vy - vehicle.vy)
                    neighbor_count += 1
        
        if neighbor_count > 0:
            fx /= neighbor_count
            fy /= neighbor_count
            
        return fx, fy
    
    def collision_avoidance(self, vehicle):
        """碰撞避免力"""
        fx, fy = 0, 0
        
        # 与其他车辆的避障
        for other in self.vehicles + [self.target]:
            if other.vehicle_id != vehicle.vehicle_id:
                distance = vehicle.distance_to(other)
                if distance < vehicle.safety_distance and distance > 0:
                    # 排斥力
                    repulsion_strength = self.k_avoidance * (1/distance - 1/vehicle.safety_distance)
                    direction_x = (vehicle.x - other.x) / distance
                    direction_y = (vehicle.y - other.y) / distance
                    
                    fx += repulsion_strength * direction_x
                    fy += repulsion_strength * direction_y
        
        return fx, fy
    
    def boundary_control(self, vehicle):
        """边界控制力"""
        fx, fy = 0, 0
        boundary_margin = 1.0
        
        # 左边界
        if vehicle.x < boundary_margin:
            fx += self.k_boundary * (boundary_margin - vehicle.x)
        # 右边界
        if vehicle.x > self.map_size - boundary_margin:
            fx -= self.k_boundary * (vehicle.x - (self.map_size - boundary_margin))
        # 下边界
        if vehicle.y < boundary_margin:
            fy += self.k_boundary * (boundary_margin - vehicle.y)
        # 上边界
        if vehicle.y > self.map_size - boundary_margin:
            fy -= self.k_boundary * (vehicle.y - (self.map_size - boundary_margin))
            
        return fx, fy
    
    def update_vehicles(self, dt):
        """更新所有车辆状态"""
        for vehicle in self.vehicles:
            # 计算各种控制力
            f_formation = self.formation_control(vehicle)
            f_consensus = self.consensus_control(vehicle)
            f_avoidance = self.collision_avoidance(vehicle)
            f_boundary = self.boundary_control(vehicle)
            
            # 合成控制力
            total_fx = f_formation[0] + f_consensus[0] + f_avoidance[0] + f_boundary[0]
            total_fy = f_formation[1] + f_consensus[1] + f_avoidance[1] + f_boundary[1]
            
            # 限制加速度
            acceleration = math.sqrt(total_fx**2 + total_fy**2)
            if acceleration > vehicle.max_acceleration:
                total_fx = total_fx / acceleration * vehicle.max_acceleration
                total_fy = total_fy / acceleration * vehicle.max_acceleration
            
            # 更新速度
            vehicle.vx += total_fx * dt
            vehicle.vy += total_fy * dt
            
            # 更新位置
            vehicle.update_position(dt)
    
    def is_formation_complete(self):
        """检查编队是否完成并判断是否成功围捕"""
        # 检查所有追捕车辆是否到达期望位置
        for vehicle in self.vehicles:
            desired_x, desired_y = self.get_desired_position(vehicle.vehicle_id)
            distance_to_desired = math.sqrt((vehicle.x - desired_x)**2 + (vehicle.y - desired_y)**2)
            if distance_to_desired > 0.3:  # 允许的误差范围
                return False
        
        # 检查是否形成有效围捕（所有车辆都在目标车辆周围的合适位置）
        angles = []
        for vehicle in self.vehicles:
            dx = vehicle.x - self.target.x
            dy = vehicle.y - self.target.y
            angle = math.atan2(dy, dx)
            if angle < 0:
                angle += 2 * math.pi
            angles.append(angle)
        
        # 对角度进行排序
        angles.sort()
        
        # 检查相邻车辆之间的角度差是否合适（约90度）
        angles.append(angles[0] + 2 * math.pi)  # 添加首个角度的2π值以处理首尾相连
        for i in range(len(angles) - 1):
            angle_diff = angles[i + 1] - angles[i]
            if abs(angle_diff - math.pi/2) > math.pi/4:  # 允许45度的误差
                return False
        
        return True

class Simulation:
    """仿真类"""
    def __init__(self):
        self.map_size = 10
        self.dt = 0.05
        
        # 创建目标车辆（故障车，位于中央）
        self.target = Vehicle(self.map_size/2, self.map_size/2, -1, is_target=True)
        
        # 创建四辆围捕车辆（初始位置在地图角落）
        self.vehicles = [
            Vehicle(2, 2, 0),  # 左下
            Vehicle(8, 2, 1),  # 右下
            Vehicle(8, 8, 2),  # 右上
            Vehicle(2, 8, 3)   # 左上
        ]
        
        # 创建控制器
        self.controller = FormationController(self.vehicles, self.target, self.map_size)
        
        # 仿真状态
        self.time = 0
        self.is_complete = False
        
    def step(self):
        """仿真一步"""
        # 目标车辆随机游走（模拟故障车的不可预测行为）
        if np.random.random() < 0.1:  # 10%概率改变方向
            self.target.vx = np.random.uniform(-0.3, 0.3)
            self.target.vy = np.random.uniform(-0.3, 0.3)
        
        # 更新目标车辆位置
        self.target.update_position(self.dt)
        
        # 确保目标车辆在边界内
        self.target.x = max(1, min(self.map_size-1, self.target.x))
        self.target.y = max(1, min(self.map_size-1, self.target.y))
        
        # 更新围捕车辆
        self.controller.update_vehicles(self.dt)
        
        # 检查是否完成编队
        self.is_complete = self.controller.is_formation_complete()
        
        # 更新时间
        self.time += self.dt
        
    def get_state(self):
        """获取当前状态"""
        state = {
            'time': self.time,
            'target': (self.target.x, self.target.y),
            'vehicles': [(v.x, v.y) for v in self.vehicles],
            'is_complete': self.is_complete
        }
        return state

def visualize_simulation():
    """可视化仿真"""
    sim = Simulation()
    
    fig, ax = plt.subplots(figsize=(10, 10))
    ax.set_xlim(0, sim.map_size)
    ax.set_ylim(0, sim.map_size)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_title('UAV Formation and Capture Simulation', fontsize=16)
    
    # 创建图形元素
    target_circle = Circle((0, 0), sim.target.radius, color='red', alpha=0.7, label='Target')
    vehicle_circles = [Circle((0, 0), v.radius, color='blue', alpha=0.7) for v in sim.vehicles]
    
    ax.add_patch(target_circle)
    for circle in vehicle_circles:
        ax.add_patch(circle)
    
    # 添加车辆编号
    vehicle_texts = [ax.text(0, 0, str(i), ha='center', va='center', fontweight='bold') 
                    for i in range(len(sim.vehicles))]
    target_text = ax.text(0, 0, 'T', ha='center', va='center', fontweight='bold', color='white')
    
    # 状态文本
    status_text = ax.text(0.02, 0.98, '', transform=ax.transAxes, 
                         verticalalignment='top', fontsize=12,
                         bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    ax.legend()
    
    def animate(frame):
        # 运行仿真步骤
        for _ in range(5):  # 每帧运行5步以加快速度
            sim.step()
        
        state = sim.get_state()
        
        # 更新目标车辆位置
        target_circle.center = state['target']
        target_text.set_position(state['target'])
        
        # 更新围捕车辆位置
        for i, (circle, text) in enumerate(zip(vehicle_circles, vehicle_texts)):
            circle.center = state['vehicles'][i]
            text.set_position(state['vehicles'][i])
        
        # 更新状态信息
        status_info = f"Time: {state['time']:.1f}s\n"
        status_info += f"Formation Status: {'Captured!' if state['is_complete'] else 'Pursuing...'}\n"
        
        # 计算车辆间距离
        distances = []
        for i, vehicle in enumerate(sim.vehicles):
            dist = vehicle.distance_to(sim.target)
            distances.append(dist)
        
        status_info += f"Avg Distance: {np.mean(distances):.2f}\n"
        status_info += f"Min Distance: {np.min(distances):.2f}"
        
        status_text.set_text(status_info)
        
        return [target_circle, target_text] + vehicle_circles + vehicle_texts + [status_text]
    
    # 创建动画
    anim = animation.FuncAnimation(fig, animate, frames=1000, interval=50, blit=True, repeat=True)
    
    plt.tight_layout()
    plt.show()
    
    return anim

def run_simulation_analysis():
    """运行仿真分析"""
    sim = Simulation()
    
    # 记录数据
    times = []
    distances = []
    formation_errors = []
    
    print("Starting simulation...")
    print("Initial state:")
    print(f"Target position: ({sim.target.x:.2f}, {sim.target.y:.2f})")
    for i, vehicle in enumerate(sim.vehicles):
        print(f"Vehicle {i} position: ({vehicle.x:.2f}, {vehicle.y:.2f})")
    
    # 运行仿真
    max_steps = 2000
    for step in range(max_steps):
        sim.step()
        
        if step % 100 == 0:  # 每100步记录一次
            state = sim.get_state()
            times.append(state['time'])
            
            # 计算平均距离
            avg_dist = np.mean([v.distance_to(sim.target) for v in sim.vehicles])
            distances.append(avg_dist)
            
            # 计算编队误差
            formation_error = 0
            for vehicle in sim.vehicles:
                desired_x, desired_y = sim.controller.get_desired_position(vehicle.vehicle_id)
                error = math.sqrt((vehicle.x - desired_x)**2 + (vehicle.y - desired_y)**2)
                formation_error += error
            formation_errors.append(formation_error / len(sim.vehicles))
            
            print(f"Step {step}: Time={state['time']:.1f}s, Avg Distance={avg_dist:.2f}, Formation Error={formation_error/len(sim.vehicles):.2f}")
        
        if sim.is_complete:
            print(f"\nTarget Captured! Time taken: {sim.time:.1f}s")
            break
    
    # 绘制分析图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    ax1.plot(times, distances, 'b-', linewidth=2, label='平均距离')
    ax1.axhline(y=sim.controller.formation_radius, color='r', linestyle='--', label='目标距离')
    ax1.set_xlabel('Time (s)')
    ax1.set_ylabel('Distance')
    ax1.set_title('Average Distance to Target')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    ax2.plot(times, formation_errors, 'g-', linewidth=2, label='Formation Error')
    ax2.set_xlabel('Time (s)')
    ax2.set_ylabel('Error')
    ax2.set_title('Formation Error over Time')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 最终状态
    print("\nFinal state:")
    print(f"Target position: ({sim.target.x:.2f}, {sim.target.y:.2f})")
    for i, vehicle in enumerate(sim.vehicles):
        desired_x, desired_y = sim.controller.get_desired_position(vehicle.vehicle_id)
        print(f"Vehicle {i}: Current({vehicle.x:.2f}, {vehicle.y:.2f}), Desired({desired_x:.2f}, {desired_y:.2f})")

if __name__ == "__main__":
    print("UAV Formation and Capture Simulation")
    print("=" * 40)
    print("1. Run Visualization")
    print("2. Run Analysis")
    
    choice = input("Choose mode (1/2): ")
    
    if choice == "1":
        print("Starting visualization...")
        anim = visualize_simulation()
    elif choice == "2":
        print("Starting analysis...")
        run_simulation_analysis()
    else:
        print("Invalid choice, running default visualization")
        anim = visualize_simulation()